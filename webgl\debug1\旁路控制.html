<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旁路控制 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <style>
        /* 旁路控制页面专用样式 */
        .control-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
        }

        .unit-count {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .unit-count-value {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 4px;
            padding: 5px 15px;
            color: #00d4ff;
            font-size: 18px;
            font-weight: bold;
        }

        .unit-count-select {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 4px;
            padding: 5px 15px;
            color: #00d4ff;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            outline: none;
        }

        .unit-count-select option {
            background: rgba(26, 31, 46, 0.95);
            color: #00d4ff;
        }

        .start-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .start-button:hover {
            background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .phase-panels {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .phase-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 20px;
        }

        .phase-title {
            text-align: center;
            color: #00d4ff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
        }

        .phase-table {
            width: 100%;
            border-collapse: collapse;
        }

        .phase-table th {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            padding: 10px;
            text-align: center;
            color: #00d4ff;
            font-weight: bold;
            font-size: 14px;
        }

        .phase-table td {
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 8px;
            text-align: center;
            color: white;
            font-size: 13px;
        }

        .phase-table tr:nth-child(even) {
            background: rgba(0, 212, 255, 0.05);
        }

        .phase-table tr:hover {
            background: rgba(0, 212, 255, 0.1);
        }

        .bypass-button {
            background: rgba(108, 117, 125, 0.8);
            color: white;
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 15px;
            padding: 5px 12px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 50px;
        }

        .bypass-button:hover {
            background: rgba(0, 212, 255, 0.3);
            border-color: rgba(0, 212, 255, 0.6);
        }

        .bypass-button.active {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
            border-color: #f44336;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }

        .unit-id {
            font-weight: bold;
            color: #00d4ff;
        }

        .value-cell {
            color: #ccc;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>旁路控制</h1>
        </div>

        <!-- 控制头部 -->
        <div class="control-header">
            <div class="unit-count">
                <span>单元数量：</span>
                <select class="unit-count-select" id="unit-count" onchange="updateUnitCount()">
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                </select>
            </div>
            <div>
                <!-- 旁路控制文本和开始按钮已移除 -->
            </div>
        </div>

        <!-- 三相面板 -->
        <div class="phase-panels">
            <!-- A相 -->
            <div class="phase-panel">
                <div class="phase-title">A相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-a-table">
                        <!-- A相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- B相 -->
            <div class="phase-panel">
                <div class="phase-title">B相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-b-table">
                        <!-- B相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- C相 -->
            <div class="phase-panel">
                <div class="phase-title">C相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-c-table">
                        <!-- C相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <button class="send-button" id="download-btn" onclick="downloadBypassSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 旁路控制页面的 JavaScript 逻辑
        let bypassStates = {
            A: {},
            B: {},
            C: {}
        };

        /**
         * 初始化三相表格
         */
        function initPhaseTables() {
            const unitCountSelect = document.getElementById('unit-count');
            const unitCount = parseInt(unitCountSelect.value);
            updatePhaseTables(unitCount);
        }

        /**
         * 更新单元数量
         */
        function updateUnitCount() {
            const unitCountSelect = document.getElementById('unit-count');
            const unitCount = parseInt(unitCountSelect.value);
            updatePhaseTables(unitCount);
            showStatusMessage(`单元数量已更新为: ${unitCount}`, 'success');
        }

        /**
         * 更新三相表格
         */
        function updatePhaseTables(unitCount) {
            const phases = ['A', 'B', 'C'];

            phases.forEach(phase => {
                const tableBody = document.getElementById(`phase-${phase.toLowerCase()}-table`);
                tableBody.innerHTML = '';

                // 重置该相的状态
                bypassStates[phase] = {};

                for (let i = 1; i <= unitCount; i++) {
                    const unitId = `${phase}${i.toString().padStart(2, '0')}`;
                    const row = document.createElement('tr');

                    row.innerHTML = `
                        <td class="unit-id">${unitId}</td>
                        <td>
                            <button class="bypass-button"
                                    id="btn-${unitId}"
                                    onclick="toggleBypass('${phase}', '${unitId}')">
                                旁路
                            </button>
                        </td>
                        <td class="value-cell" id="value-${unitId}">-</td>
                    `;

                    tableBody.appendChild(row);

                    // 初始化状态
                    bypassStates[phase][unitId] = false;
                }
            });
        }

        /**
         * 切换旁路状态
         */
        function toggleBypass(phase, unitId) {
            const button = document.getElementById(`btn-${unitId}`);
            const valueCell = document.getElementById(`value-${unitId}`);

            // 切换状态
            bypassStates[phase][unitId] = !bypassStates[phase][unitId];

            if (bypassStates[phase][unitId]) {
                button.classList.add('active');
                button.textContent = '旁路';
                valueCell.textContent = '旁路';
            } else {
                button.classList.remove('active');
                button.textContent = '旁路';
                valueCell.textContent = '-';
            }

            console.log(`${unitId} 旁路状态:`, bypassStates[phase][unitId]);
            showStatusMessage(`${unitId} ${bypassStates[phase][unitId] ? '已启用' : '已禁用'}旁路`, 'success');
        }

        /**
         * 开始旁路控制
         */
        function startBypassControl() {
            console.log('开始旁路控制');
            showStatusMessage('旁路控制已开始', 'success');
        }

        /**
         * 下载旁路设置
         */
        function downloadBypassSettings() {
            console.log('下载旁路设置');
            console.log('当前旁路状态:', bypassStates);
            showStatusMessage('旁路设置下载成功', 'success');
        }

        /**
         * 显示状态消息
         */
        function showStatusMessage(message, type = 'success') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('旁路控制页面初始化...');

            // 初始化三相表格
            initPhaseTables();

            // 初始化MQTT连接状态
            const mqttStatus = document.getElementById('mqtt-status');
            const dataTimestamp = document.getElementById('data-timestamp');

            // 模拟MQTT连接
            setTimeout(() => {
                mqttStatus.textContent = 'MQTT 已连接';
                mqttStatus.className = 'mqtt-connection-status connected';
                dataTimestamp.textContent = new Date().toLocaleString();
            }, 2000);

            // 定期更新时间戳
            setInterval(() => {
                if (mqttStatus.classList.contains('connected')) {
                    dataTimestamp.textContent = new Date().toLocaleString();
                }
            }, 1000);
        });
    </script>
</body>
</html>