关于设备操作.html的UI逻辑和业务逻辑

1. 需要订阅三个Mqtt主题，分别是：
控制指令：{"clientId":"S&D196HBJWP3B1F&201&19","username":"bydq_admin","passwd":"P14U4554X4XB5314","subscribeTopic":"/201/D196HBJWP3B1F/function/get","reportTopic":"/201/D196HBJWP3B1F/property/post","port":"1883"}
开关量报警：
{"clientId":"S&D195FEVZWYR85&198&19","username":"bydq_admin","passwd":"PCO91H46TSHM5IS8","subscribeTopic":"/198/D195FEVZWYR85/function/get","reportTopic":"/198/D195FEVZWYR85/property/post","port":"1883"}
调试参数：
{"clientId":"S&D19EOEN59V1MJ&197&19","username":"bydq_admin","passwd":"P5OGL9C1V3E7W06P","subscribeTopic":"/197/D19EOEN59V1MJ/function/get","reportTopic":"/197/D19EOEN59V1MJ/property/post","port":"1883"}

2. 点击下载下发当前页面所有修改数据；

3. 其中启动，停止，复位,无功启动，谐波消除，综合启动按钮的逻辑为，启动按钮点击后，将控制指令主题中A1的值修改为0,下发给设备；停止按钮点击后，将控制指令主题中A2的值修改为1,下发给设备；复位按钮点击后，将控制指令主题中A3的值修改为1,下发给设备；无功启动按钮点击后，将控制指令主题中A4的值修改为1,下发给设备；谐波消除按钮点击后，将控制指令主题中A5的值修改为1,下发给设备；综合控制按钮点击后，将控制指令主题中A6的值修改为1,下发给设备；其中启动按钮点击后背景色变为绿色，停止按钮点击后背景色变为红色，启动和停止按钮状态互斥，复位按钮点击后背景色为蓝色，复位按钮点击后会重置掉启动和停止按钮状态；无功启动，谐波消除，综合启动按钮点击后按钮背景色变为绿色。


4.启动，停止，复位状态灯的逻辑为，从开关量报警主题中订阅到的ID为39_5,39_6,39_11的值；当39_5的值为1时，启动状态灯亮；当39_5的值为0，停止状态灯灭；当39_6和39_11的值任意一个为1时，复位状态灯亮，无功启动，谐波消除，综合启动的状态灯为当对应按钮点击后灯就会亮。


5.主控方式按钮逻辑如下：
   点击固定补偿按钮，将调试参数主题中ID为1914_RPC的值修改为1,下发给设备；
   点击动态补偿按钮，将调试参数主题中ID为1914_OM的值修改为0和ID为1914_RPC的值修改为0,下发给设备；
   点击电压控制按钮，将调试参数主题中ID为1914_OM的值修改为2和ID为1914_RPC的值修改为0,下发给设备；
   点击功率因素按钮，将调试参数主题中ID为1914_OM的值修改为1和ID为1914_RPC的值修改为0,下发给设备；
   点击无功电压按钮，将调试参数主题中ID为1914_OM的值修改为3和ID为1914_RPC的值修改为0,下发给设备；
   按钮点击后，按钮背景色变为绿色，主控方式按钮状态都为互斥的。


主控方式中的状态灯根据调试参数主题中ID为1914_OM和1914_RPC的值来显示，当1914_RPC的值为1时，固定补偿状态灯亮；当1914_OM的值为0且1914_RPC的值为0时，动态补偿状态灯亮；当1914_OM的值为2且1914_RPC的值为0时，电压控制状态灯亮；当1914_OM的值为1且1914_RPC的值为0时，功率因素状态灯亮；当1914_OM的值为4且1914_RPC的值为0时，无功电压状态灯亮；


