<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备操作 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <style>
        /* 设备操作页面专用样式 - 参考保护使能页面风格 */
        .main-content {
            display: flex;
            flex-direction: row;
            gap: 20px;
            padding-bottom: 80px;
        }

        .operation-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 20px;
        }

        .operation-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .operation-table th {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            padding: 12px;
            text-align: center;
            color: #00d4ff;
            font-weight: bold;
            font-size: 14px;
        }

        .operation-table td {
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 10px;
            text-align: center;
            color: white;
            font-size: 13px;
        }

        .operation-table tr:nth-child(even) {
            background: rgba(0, 212, 255, 0.05);
        }

        .operation-table tr:hover {
            background: rgba(0, 212, 255, 0.1);
        }

        .control-button {
            width: 80px;
            height: 35px;
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 18px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
        }

        .start-button {
            background: rgba(76, 175, 80, 0.8);
        }

        .start-button:hover {
            background: rgba(76, 175, 80, 1);
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .stop-button {
            background: rgba(244, 67, 54, 0.8);
        }

        .stop-button:hover {
            background: rgba(244, 67, 54, 1);
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }

        .reset-button {
            background: rgba(33, 150, 243, 0.8);
        }

        .reset-button:hover {
            background: rgba(33, 150, 243, 1);
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
        }

        .option-button {
            width: 80px;
            height: 35px;
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 18px;
            background: rgba(42, 49, 66, 0.7);
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
        }

        .option-button:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.6);
        }

        .option-button.active {
            background: rgba(0, 212, 255, 0.8);
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .status-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #666;
            border: 2px solid #333;
            transition: all 0.3s ease;
            display: inline-block;
            margin-left: 10px;
        }

        .status-indicator.active {
            background: #4CAF50;
            border-color: #45a049;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        }

        .status-indicator.stop {
            background: #f44336;
            border-color: #da190b;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
        }

        .button-cell {
            text-align: center;
            vertical-align: middle;
        }

        .status-cell {
            text-align: center;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>设备操作</h1>
        </div>

        <div class="main-content">
            <!-- 运行状态面板 -->
            <div class="operation-panel">
                <div class="panel-title">运行状态</div>
                <table class="operation-table">
                    <thead>
                        <tr>
                            <th>操作</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button start-button" onclick="startOperation()">启动</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="start-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button stop-button" onclick="stopOperation()">停止</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator stop" id="stop-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button reset-button" onclick="resetOperation()">复位</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="reset-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 主控方式面板 -->
            <div class="operation-panel">
                <div class="panel-title">主控方式</div>
                <table class="operation-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="fixedCompensation" onclick="selectMainControl(this)">固定补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="fixed-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="dynamicCompensation" onclick="selectMainControl(this)">动态补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="dynamic-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="powerFactor" onclick="selectMainControl(this)">功率因数</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="power-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 辅控方式面板 -->
            <div class="operation-panel">
                <div class="panel-title">辅控方式</div>
                <table class="operation-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="harmonicElimination" onclick="selectAuxiliaryControl(this)">谐波消除</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="harmonic-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <button class="send-button" id="download-btn" onclick="downloadSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 设备操作页面的 JavaScript 逻辑
        let currentMainControl = null;
        let currentAuxiliaryControl = null;

        function startOperation() {
            console.log('启动操作');
            updateStatusIndicator('start-indicator', true);
            updateStatusIndicator('stop-indicator', false);
            showStatusMessage('设备启动成功', 'success');
        }

        function stopOperation() {
            console.log('停止操作');
            updateStatusIndicator('stop-indicator', true);
            updateStatusIndicator('start-indicator', false);
            showStatusMessage('设备停止成功', 'success');
        }

        function resetOperation() {
            console.log('复位操作');
            updateStatusIndicator('reset-indicator', true);
            setTimeout(() => {
                updateStatusIndicator('reset-indicator', false);
            }, 2000);
            showStatusMessage('设备复位成功', 'success');
        }

        function selectMainControl(button) {
            // 清除之前的选择
            document.querySelectorAll('.operation-panel:nth-child(2) .option-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('.operation-panel:nth-child(2) .status-indicator').forEach(indicator => {
                indicator.classList.remove('active');
            });

            // 设置新的选择
            button.classList.add('active');
            const indicatorId = getIndicatorId(button.dataset.value, 'main');
            updateStatusIndicator(indicatorId, true);

            currentMainControl = button.dataset.value;
            console.log('选择主控方式:', button.textContent);
            showStatusMessage(`主控方式设置为: ${button.textContent}`, 'success');
        }

        function selectAuxiliaryControl(button) {
            // 清除之前的选择
            document.querySelectorAll('.operation-panel:nth-child(3) .option-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('.operation-panel:nth-child(3) .status-indicator').forEach(indicator => {
                indicator.classList.remove('active');
            });

            // 设置新的选择
            button.classList.add('active');
            const indicatorId = getIndicatorId(button.dataset.value, 'auxiliary');
            updateStatusIndicator(indicatorId, true);

            currentAuxiliaryControl = button.dataset.value;
            console.log('选择辅控方式:', button.textContent);
            showStatusMessage(`辅控方式设置为: ${button.textContent}`, 'success');
        }

        function getIndicatorId(value, type) {
            const mapping = {
                'fixedCompensation': 'fixed-indicator',
                'dynamicCompensation': 'dynamic-indicator',
                'powerFactor': 'power-indicator',
                'harmonicElimination': 'harmonic-indicator'
            };
            return mapping[value];
        }

        function updateStatusIndicator(indicatorId, active) {
            const indicator = document.getElementById(indicatorId);
            if (indicator) {
                if (active) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }
            }
        }

        function showStatusMessage(message, type = 'success') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        function downloadSettings() {
            console.log('下载设置');
            const settings = {
                mainControl: currentMainControl,
                auxiliaryControl: currentAuxiliaryControl
            };
            console.log('当前设置:', settings);
            showStatusMessage('设置下载成功', 'success');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('设备操作页面初始化...');

            // 初始化MQTT连接状态
            const mqttStatus = document.getElementById('mqtt-status');
            const dataTimestamp = document.getElementById('data-timestamp');

            // 模拟MQTT连接
            setTimeout(() => {
                mqttStatus.textContent = 'MQTT 已连接';
                mqttStatus.className = 'mqtt-connection-status connected';
                dataTimestamp.textContent = new Date().toLocaleString();
            }, 2000);

            // 定期更新时间戳
            setInterval(() => {
                if (mqttStatus.classList.contains('connected')) {
                    dataTimestamp.textContent = new Date().toLocaleString();
                }
            }, 1000);
        });
    </script>
</body>
</html>