<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滤波控制 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 滤波控制专用样式 -->
    <style>
        /* 滤波控制专用样式扩展 */
        .filter-control-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: none;
            display: flex;
            flex-direction: column;
            gap: 10px;
            position: relative;
            padding-bottom: 70px;
            overflow: visible;
            min-height: 0;
            height: auto;
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 整数输入框样式 */
        .integer-input {
            width: 80px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .integer-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .integer-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 浮点数输入框样式 */
        .float-input {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .float-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .float-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 开关控件容器样式 - 水平布局，开关在左，文本在右 */
        .switch-control-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 100%;
            padding: 4px;
            gap: 8px;
        }

        /* 重写开关样式以确保一致性 */
        .toggle-switch {
            position: relative;
            width: 60px !important;
            height: 30px !important;
            background: #dc3545;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
            flex-shrink: 0;
            margin: 0;
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(30px);
        }

        /* 开关状态文本样式 - 放在开关右侧 */
        .switch-status-text {
            font-size: 11px;
            color: #7a8ba0;
            text-align: left;
            width: 60px;
            height: 30px;
            line-height: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0;
            flex-shrink: 0;
        }

        .switch-status-text.active {
            color: #00d4ff;
        }

        /* 调整参数设置列宽度以适应不同控件 */
        .param-setting {
            width: 130px;
            text-align: center;
            vertical-align: middle;
            padding: 3px;
        }

        /* 对称布局样式 - 4行2列对称网格 */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto auto;
            gap: 12px;
            height: auto;
            max-height: none;
        }

        /* 第一行：滤波控制1（左侧） - 滤波控制2（右侧） */
        .filter-control-panel.group1 {
            grid-column: 1;
            grid-row: 1;
        }

        .filter-control-panel.group5 {
            grid-column: 2;
            grid-row: 1;
        }

        /* 第二行：滤波通道指令电流1（左侧） - 滤波通道指令电流2（右侧） */
        .filter-control-panel.group2 {
            grid-column: 1;
            grid-row: 2;
        }

        .filter-control-panel.group6 {
            grid-column: 2;
            grid-row: 2;
        }

        /* 第三行：滤波通道输出电流上限1（左侧） - 滤波通道输出电流上限2（右侧） */
        .filter-control-panel.group3 {
            grid-column: 1;
            grid-row: 3;
        }

        .filter-control-panel.group7 {
            grid-column: 2;
            grid-row: 3;
        }

        /* 第四行：滤波使能控制（占据全宽） */
        .filter-control-panel.group4 {
            grid-column: 1 / 3;
            grid-row: 4;
        }

        .filter-control-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px;
            overflow: visible;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        /* 滤波使能控制面板特殊样式 - 横向布局每行4个参数 */
        .filter-control-panel.group4 .params-table {
            width: 100%;
            table-layout: fixed;
        }

        .filter-control-panel.group4 .params-table thead tr {
            display: grid;
            grid-template-columns:
                60px 180px 140px 80px    /* 第1个参数：序号 参数名称 设定值 当前值 */
                60px 180px 140px 80px    /* 第2个参数：序号 参数名称 设定值 当前值 */
                60px 180px 140px 80px    /* 第3个参数：序号 参数名称 设定值 当前值 */
                60px 180px 140px 80px;   /* 第4个参数：序号 参数名称 设定值 当前值 */
            gap: 3px;
            align-items: center;
        }

        .filter-control-panel.group4 .params-table tbody tr {
            display: grid;
            grid-template-columns:
                60px 180px 140px 80px    /* 第1个参数：序号 参数名称 设定值 当前值 */
                60px 180px 140px 80px    /* 第2个参数：序号 参数名称 设定值 当前值 */
                60px 180px 140px 80px    /* 第3个参数：序号 参数名称 设定值 当前值 */
                60px 180px 140px 80px;   /* 第4个参数：序号 参数名称 设定值 当前值 */
            gap: 3px;
            margin-bottom: 8px;
            align-items: center;
            min-height: 50px;
        }

        .filter-control-panel.group4 .params-table th,
        .filter-control-panel.group4 .params-table td {
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 6px 4px;
            text-align: center;
            font-size: 12px;
            background: rgba(42, 49, 66, 0.7);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .filter-control-panel.group4 .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            font-weight: bold;
            height: 35px;
        }

        .filter-control-panel.group4 .params-table td {
            height: 50px;
        }

        /* 滤波使能控制面板的表头样式 */
        .filter-control-panel.group4 .params-table thead {
            margin-bottom: 8px;
        }

        /* 滤波使能控制面板中的序号列样式 */
        .filter-control-panel.group4 .param-index {
            font-size: 11px;
            color: #7a8ba0;
            font-weight: bold;
            width: 60px !important;
        }

        /* 滤波使能控制面板中的参数名称列样式 */
        .filter-control-panel.group4 .param-name {
            font-size: 11px;
            color: #ffffff;
            text-align: center;
            width: 180px !important;
            padding: 0 4px;
            line-height: 1.2;
        }

        /* 滤波使能控制面板中的设定值列样式 */
        .filter-control-panel.group4 .param-setting {
            width: 140px !important;
            padding: 0 4px;
        }

        /* 滤波使能控制面板中的当前值列样式 */
        .filter-control-panel.group4 .param-current {
            font-size: 11px;
            color: #00d4ff;
            font-weight: bold;
            width: 80px !important;
        }

        /* 滤波使能控制面板中的开关控件样式调整 */
        .filter-control-panel.group4 .switch-control-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            gap: 6px;
        }

        .filter-control-panel.group4 .toggle-switch {
            width: 50px !important;
            height: 25px !important;
            border-radius: 12px;
        }

        .filter-control-panel.group4 .toggle-switch::before {
            width: 20px;
            height: 20px;
            top: 2px;
            left: 2px;
        }

        .filter-control-panel.group4 .toggle-switch.active::before {
            transform: translateX(25px);
        }

        .filter-control-panel.group4 .switch-status-text {
            font-size: 10px;
            width: 50px;
            height: 25px;
            line-height: 25px;
            text-align: center;
        }

        /* 表格容器样式 - 移除内部滚动 */
        .table-container {
            flex: none;
            overflow-y: visible;
            overflow-x: hidden;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px;
            padding: 6px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 表格样式优化 - 支持每行四个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 6px 3px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 12px;
        }

        .params-table td {
            padding: 3px 2px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 50px;
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            font-weight: bold;
            width: 30px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 12px;
            color: #ffffff;
            text-align: left;
            padding-left: 4px;
            line-height: 1.2;
            max-width: 120px;
            width: 120px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
            width: 80px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 110px;
            text-align: center;
            padding: 0 5px;
        }

        /* 确保页面可以垂直滚动 */
        html, body {
            height: auto;
            min-height: 100vh;
            overflow-y: visible;
        }

        /* 触摸屏优化样式 */
        @media (max-width: 1920px) {
            .toggle-switch {
                width: 70px !important;
                height: 35px !important;
            }

            .toggle-switch::before {
                width: 28px;
                height: 28px;
            }

            .toggle-switch.active::before {
                transform: translateX(35px);
            }

            .integer-input,
            .float-input {
                height: 35px;
                font-size: 16px;
            }

            .params-table td {
                height: 55px;
            }
        }

        /* 响应式布局调整 */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
            }
            
            .filter-control-panel {
                grid-column: 1 !important;
                grid-row: auto !important;
            }
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>滤波控制</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 第一行左侧：滤波控制1 -->
                <div class="filter-control-panel group1">
                    <div class="panel-title">滤波控制1</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-1">
                                <!-- 滤波控制1参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第一行右侧：滤波控制2 -->
                <div class="filter-control-panel group5">
                    <div class="panel-title">滤波控制2</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-5">
                                <!-- 滤波控制2参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第二行左侧：滤波通道指令电流1 -->
                <div class="filter-control-panel group2">
                    <div class="panel-title">滤波通道指令电流1</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-2">
                                <!-- 滤波通道指令电流1参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第二行右侧：滤波通道指令电流2 -->
                <div class="filter-control-panel group6">
                    <div class="panel-title">滤波通道指令电流2</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-6">
                                <!-- 滤波通道指令电流2参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第三行左侧：滤波通道输出电流上限1 -->
                <div class="filter-control-panel group3">
                    <div class="panel-title">滤波通道输出电流上限1</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-3">
                                <!-- 滤波通道输出电流上限1参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第三行右侧：滤波通道输出电流上限2 -->
                <div class="filter-control-panel group7">
                    <div class="panel-title">滤波通道输出电流上限2</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-7">
                                <!-- 滤波通道输出电流上限2参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第四行：滤波使能控制（占据全宽，每行4个参数） -->
                <div class="filter-control-panel group4">
                    <div class="panel-title">滤波使能控制</div>
                    <div class="table-container">
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                    <th>序号</th>
                                    <th>参数名称</th>
                                    <th>设定值</th>
                                    <th>当前值</th>
                                </tr>
                            </thead>
                            <tbody id="filter-table-4">
                                <!-- 滤波使能控制参数将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="handleSendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 确保全局变量可访问
        window.parameterManager = null;

        /**
         * 滤波控制页面配置
         * 定义滤波控制参数列表和页面设置
         */

        // 第1组 - 滤波控制1（8个参数）
        const filterControl1Group = [
            { mqttId: '2301_FC1O', name: '滤波通道1次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2301_FC2O', name: '滤波通道2次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2301_FC3O', name: '滤波通道3次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2301_FC4O', name: '滤波通道4次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2301_FC1PS', name: '滤波通道1相序', type: 'switch', options: ['正序', '负序'] },
            { mqttId: '2301_FC2PS', name: '滤波通道2相序', type: 'switch', options: ['正序', '负序'] },
            { mqttId: '2301_FC3PS', name: '滤波通道3相序', type: 'switch', options: ['正序', '负序'] },
            { mqttId: '2301_FC4PS', name: '滤波通道4相序', type: 'switch', options: ['正序', '负序'] }
        ];

        // 第2组 - 滤波通道指令电流1（4个参数）
        const filterCurrent1Group = [
            { mqttId: 'SVG_2312', name: '滤波通道1', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2313', name: '滤波通道2', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2314', name: '滤波通道3', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2315', name: '滤波通道4', type: 'float', min: -99999.00, max: 99999.00 }
        ];

        // 第3组 - 滤波通道输出电流上限1（4个参数）
        const filterLimit1Group = [
            { mqttId: 'SVG_2304', name: '滤波通道1', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2305', name: '滤波通道2', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2306', name: '滤波通道3', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2307', name: '滤波通道4', type: 'float', min: -99999.00, max: 99999.00 }
        ];

        // 第4组 - 滤波使能控制（8个参数）
        const filterEnableGroup = [
            { mqttId: '2303_FC1E', name: '滤波通道1使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC2E', name: '滤波通道2使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC3E', name: '滤波通道3使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC4E', name: '滤波通道4使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC5E', name: '滤波通道5使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC6E', name: '滤波通道6使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC7E', name: '滤波通道7使能', type: 'switch', options: ['不使能', '使能'] },
            { mqttId: '2303_FC8E', name: '滤波通道8使能', type: 'switch', options: ['不使能', '使能'] }
        ];

        // 第5组 - 滤波控制2（8个参数）
        const filterControl2Group = [
            { mqttId: '2302_FC5O', name: '滤波通道5次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2302_FC6O', name: '滤波通道6次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2302_FC7O', name: '滤波通道7次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2302_FC8O', name: '滤波通道8次数', type: 'integer', min: 2, max: 23 },
            { mqttId: '2302_FC5PS', name: '滤波通道5相序', type: 'switch', options: ['正序', '负序'] },
            { mqttId: '2302_FC6PS', name: '滤波通道6相序', type: 'switch', options: ['正序', '负序'] },
            { mqttId: '2302_FC7PS', name: '滤波通道7相序', type: 'switch', options: ['正序', '负序'] },
            { mqttId: '2302_FC8PS', name: '滤波通道8相序', type: 'switch', options: ['正序', '负序'] }
        ];

        // 第6组 - 滤波通道指令电流2（4个参数）
        const filterCurrent2Group = [
            { mqttId: 'SVG_2316', name: '滤波通道5', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2317', name: '滤波通道6', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2318', name: '滤波通道7', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2319', name: '滤波通道8', type: 'float', min: -99999.00, max: 99999.00 }
        ];

        // 第7组 - 滤波通道输出电流上限2（4个参数）
        const filterLimit2Group = [
            { mqttId: 'SVG_2308', name: '滤波通道5', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2309', name: '滤波通道6', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2310', name: '滤波通道7', type: 'float', min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2311', name: '滤波通道8', type: 'float', min: -99999.00, max: 99999.00 }
        ];

        // 合并所有参数
        const allFilterParameters = [
            ...filterControl1Group,
            ...filterCurrent1Group,
            ...filterLimit1Group,
            ...filterEnableGroup,
            ...filterControl2Group,
            ...filterCurrent2Group,
            ...filterLimit2Group
        ];

        // 页面配置对象
        const filterControlConfig = {
            pageTitle: '滤波控制',
            panelTitles: [
                '滤波控制1',
                '滤波通道指令电流1',
                '滤波通道输出电流上限1',
                '滤波使能控制',
                '滤波控制2',
                '滤波通道指令电流2',
                '滤波通道输出电流上限2'
            ],
            parameters: allFilterParameters,
            parametersPerPanel: [8, 4, 4, 8, 8, 4, 4], // 每个面板的参数数量
            parametersPerRow: 2 // 每行显示2个参数
        };

        /**
         * 滤波控制专用参数配置管理类
         * 扩展通用参数配置管理器以支持不同类型的控件
         */
        class FilterControlParameterManager extends ParameterConfigManager {
            constructor(config) {
                super(config);
            }

            /**
             * 初始化参数定义（重写以确保正确的序号）
             */
            initializeParams() {
                if (!this.config.parameters || !Array.isArray(this.config.parameters)) {
                    console.error('参数配置无效：parameters 必须是数组');
                    return;
                }

                this.config.parameters.forEach((param, index) => {
                    // 计算参数属于哪个面板
                    let panelIndex = 1;
                    let paramIndexInPanel = index + 1;
                    let currentIndex = 0;

                    for (let i = 0; i < this.config.parametersPerPanel.length; i++) {
                        if (currentIndex + this.config.parametersPerPanel[i] > index) {
                            panelIndex = i + 1;
                            paramIndexInPanel = index - currentIndex + 1;
                            break;
                        }
                        currentIndex += this.config.parametersPerPanel[i];
                    }

                    this.parameters.push({
                        id: `param_${index + 1}`,
                        index: paramIndexInPanel, // 在面板内的序号
                        globalIndex: index + 1,   // 全局序号
                        mqttId: param.mqttId,
                        name: param.name,
                        currentValue: param.type === 'float' ? 0.00 : 0,
                        settingValue: param.type === 'float' ? 0.00 : 0,
                        panel: panelIndex,
                        isInitialized: false
                    });
                });

                console.log(`初始化了 ${this.parameters.length} 个滤波控制参数`);
            }

            /**
             * 创建参数表格界面（重写以支持对称布局和滤波使能控制的特殊布局）
             */
            createParamTables() {
                const panelCount = this.config.panelTitles.length;
                let paramIndex = 0;

                // 更新页面标题
                if (this.config.pageTitle) {
                    const headerTitle = document.querySelector('.header h1');
                    if (headerTitle) {
                        headerTitle.textContent = this.config.pageTitle;
                    }
                }

                // 面板映射：原始面板索引 -> 新布局面板索引
                const panelMapping = {
                    1: 1, // 滤波控制1 -> group1
                    2: 2, // 滤波通道指令电流1 -> group2
                    3: 3, // 滤波通道输出电流上限1 -> group3
                    4: 4, // 滤波使能控制 -> group4
                    5: 5, // 滤波控制2 -> group5
                    6: 6, // 滤波通道指令电流2 -> group6
                    7: 7  // 滤波通道输出电流上限2 -> group7
                };

                // 为每个面板创建表格
                for (let panelIndex = 1; panelIndex <= panelCount; panelIndex++) {
                    const groupIndex = panelMapping[panelIndex];
                    const panelTitle = document.querySelector(`.filter-control-panel.group${groupIndex} .panel-title`);
                    const tableBody = document.getElementById(`filter-table-${groupIndex}`);

                    if (panelTitle && this.config.panelTitles[panelIndex - 1]) {
                        panelTitle.textContent = this.config.panelTitles[panelIndex - 1];
                    }

                    if (tableBody) {
                        // 获取当前面板的参数数量
                        const paramCount = this.config.parametersPerPanel[panelIndex - 1];

                        // 特殊处理滤波使能控制面板（每行4个参数）
                        if (panelIndex === 4) {
                            // 滤波使能控制：每行4个参数，共8个参数分2行
                            for (let i = 0; i < paramCount; i += 4) {
                                const params = [];
                                for (let j = 0; j < 4 && (paramIndex + i + j) < this.parameters.length; j++) {
                                    params.push(this.parameters[paramIndex + i + j]);
                                }

                                if (params.length > 0) {
                                    const row = this.createFourParamRow(params);
                                    tableBody.appendChild(row);
                                }
                            }
                        } else {
                            // 其他面板：每行2个参数
                            for (let i = 0; i < paramCount; i += 2) {
                                const leftParam = this.parameters[paramIndex + i];
                                const rightParam = this.parameters[paramIndex + i + 1];

                                if (leftParam) {
                                    const row = this.createTwoParamRow(leftParam, rightParam);
                                    tableBody.appendChild(row);
                                }
                            }
                        }

                        paramIndex += paramCount;
                    }
                }

                // 初始化所有参数的高亮状态
                this.initializeAllHighlights();
            }

            /**
             * 创建包含两个参数的表格行
             * @param {Object} leftParam - 左侧参数对象
             * @param {Object} rightParam - 右侧参数对象（可能为null）
             */
            createTwoParamRow(leftParam, rightParam) {
                const row = document.createElement('tr');

                // 创建左侧参数的HTML
                const leftHTML = this.createSingleParamHTML(leftParam);

                // 创建右侧参数的HTML（如果存在）
                let rightHTML = '';
                if (rightParam) {
                    rightHTML = this.createSingleParamHTML(rightParam);
                } else {
                    // 如果没有右侧参数，创建空的占位单元格
                    rightHTML = `
                        <td class="param-index"></td>
                        <td class="param-name"></td>
                        <td class="param-setting"></td>
                        <td class="param-current"></td>
                    `;
                }

                row.innerHTML = leftHTML + rightHTML;
                return row;
            }

            /**
             * 创建包含四个参数的表格行（用于滤波使能控制面板）
             * @param {Array} params - 参数对象数组（最多4个）
             */
            createFourParamRow(params) {
                const row = document.createElement('tr');
                let rowHTML = '';

                // 为每个参数创建HTML
                for (let i = 0; i < 4; i++) {
                    if (i < params.length && params[i]) {
                        rowHTML += this.createSingleParamHTML(params[i]);
                    } else {
                        // 创建空的占位单元格
                        rowHTML += `
                            <td class="param-index"></td>
                            <td class="param-name"></td>
                            <td class="param-setting"></td>
                            <td class="param-current"></td>
                        `;
                    }
                }

                row.innerHTML = rowHTML;
                return row;
            }

            /**
             * 创建单个参数的HTML内容
             * @param {Object} param - 参数对象
             */
            createSingleParamHTML(param) {
                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);

                let controlHTML = '';

                // 根据参数类型创建不同的控件
                switch (paramConfig.type) {
                    case 'switch':
                        controlHTML = this.createSwitchControl(param, paramConfig);
                        break;
                    case 'integer':
                        controlHTML = this.createIntegerControl(param, paramConfig);
                        break;
                    case 'float':
                        controlHTML = this.createFloatControl(param, paramConfig);
                        break;
                    default:
                        controlHTML = this.createSwitchControl(param, paramConfig);
                }

                // 获取当前值的显示文本
                const currentValueText = this.getCurrentValueDisplayText(param, paramConfig);

                return `
                    <td class="param-index" data-param-id="${param.id}" data-cell-type="index">${param.index}</td>
                    <td class="param-name" data-param-id="${param.id}" data-cell-type="name">${param.name}</td>
                    <td class="param-setting" data-param-id="${param.id}" data-cell-type="setting">
                        ${controlHTML}
                    </td>
                    <td class="param-current" data-param-id="${param.id}" data-cell-type="current" id="current-${param.id}">${currentValueText}</td>
                `;
            }

            /**
             * 获取当前值的显示文本
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            getCurrentValueDisplayText(param, config) {
                switch (config.type) {
                    case 'switch':
                        return config.options && config.options[param.currentValue] ? config.options[param.currentValue] : param.currentValue;
                    case 'integer':
                        return param.currentValue;
                    case 'float':
                        return (param.currentValue || 0.00).toFixed(2);
                    default:
                        return param.currentValue;
                }
            }

            /**
             * 创建开关控件
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            createSwitchControl(param, config) {
                const statusText = config.options ? config.options[param.settingValue] || param.settingValue : param.settingValue;
                return `
                    <div class="switch-control-container">
                        <div class="toggle-switch ${param.settingValue ? 'active' : ''}"
                             id="toggle-${param.id}"
                             onclick="filterControlManager.toggleParam('${param.id}')">
                        </div>
                        <div class="switch-status-text ${param.settingValue ? 'active' : ''}" id="status-text-${param.id}">
                            ${statusText}
                        </div>
                    </div>
                `;
            }

            /**
             * 创建整数输入控件
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            createIntegerControl(param, config) {
                return `
                    <div class="control-container">
                        <input type="number"
                               class="integer-input"
                               id="input-${param.id}"
                               value="${param.settingValue}"
                               min="${config.min || 0}"
                               max="${config.max || 100}"
                               onchange="filterControlManager.updateIntegerParam('${param.id}', this.value)"
                               onblur="filterControlManager.validateIntegerParam('${param.id}', this)">
                    </div>
                `;
            }

            /**
             * 创建浮点数输入控件
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            createFloatControl(param, config) {
                // 确保设定值格式化为2位小数
                const formattedValue = (param.settingValue || 0.00).toFixed(2);

                return `
                    <div class="control-container">
                        <input type="number"
                               class="float-input"
                               id="input-${param.id}"
                               value="${formattedValue}"
                               min="${config.min || -99999.00}"
                               max="${config.max || 99999.00}"
                               step="0.01"
                               onchange="filterControlManager.updateFloatParam('${param.id}', this.value)"
                               onblur="filterControlManager.validateFloatParam('${param.id}', this)">
                    </div>
                `;
            }

            /**
             * 切换开关参数
             * @param {string} paramId - 参数ID
             */
            toggleParam(paramId) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);
                if (paramConfig.type !== 'switch') return;

                // 切换设定值
                param.settingValue = param.settingValue ? 0 : 1;

                // 更新界面
                const toggle = document.getElementById(`toggle-${paramId}`);
                const statusText = document.getElementById(`status-text-${paramId}`);

                if (param.settingValue) {
                    toggle.classList.add('active');
                    statusText.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                    statusText.classList.remove('active');
                }

                // 更新状态文本
                if (paramConfig.options) {
                    statusText.textContent = paramConfig.options[param.settingValue] || param.settingValue;
                } else {
                    statusText.textContent = param.settingValue;
                }

                // 更新高亮状态
                this.updateHighlightStatus(paramId);

                // 记录修改
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 更新整数参数
             * @param {string} paramId - 参数ID
             * @param {string} value - 新值
             */
            updateIntegerParam(paramId, value) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);
                if (!paramConfig) return;

                const numValue = parseInt(value, 10);
                const min = paramConfig.min || 0;
                const max = paramConfig.max || 100;

                if (isNaN(numValue)) {
                    // 使用默认值
                    const defaultValue = Math.max(min, Math.min(max, param.settingValue || min));
                    param.settingValue = defaultValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = defaultValue;
                    }
                } else if (numValue < min || numValue > max) {
                    // 超出范围，使用边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = boundaryValue;
                    }

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${boundaryValue}`, 'warning');
                } else {
                    // 有效值
                    param.settingValue = numValue;
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 验证整数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateIntegerParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);

                if (!param || !paramConfig) return;

                const value = parseInt(inputElement.value, 10);
                const min = paramConfig.min || 0;
                const max = paramConfig.max || 100;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = Math.max(min, Math.min(max, param.settingValue || min));
                    param.settingValue = defaultValue;
                    inputElement.value = defaultValue;

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${defaultValue}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                } else {
                    // 有效值
                    param.settingValue = value;
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新浮点数参数
             * @param {string} paramId - 参数ID
             * @param {string} value - 新值
             */
            updateFloatParam(paramId, value) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);
                if (!paramConfig) return;

                const numValue = parseFloat(value);
                const min = paramConfig.min || -99999.00;
                const max = paramConfig.max || 99999.00;

                if (isNaN(numValue)) {
                    // 使用默认值0.00
                    param.settingValue = 0.00;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }
                } else if (numValue < min || numValue > max) {
                    // 超出范围，使用边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${param.settingValue.toFixed(2)}`, 'warning');
                } else {
                    // 有效值，保留完整精度用于计算
                    param.settingValue = numValue;
                    // 确保输入框显示格式化后的值
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue.toFixed(2)}`);
            }

            /**
             * 验证浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const paramConfig = this.config.parameters.find(p => p.mqttId === param.mqttId);
                if (!paramConfig) return;

                const value = parseFloat(inputElement.value);
                const min = paramConfig.min || -99999.00;
                const max = paramConfig.max || 99999.00;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = param.settingValue || 0.00;
                    param.settingValue = defaultValue;
                    inputElement.value = param.settingValue.toFixed(2);

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${param.settingValue.toFixed(2)}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                } else {
                    // 有效值，确保格式化显示
                    param.settingValue = value;
                    inputElement.value = param.settingValue.toFixed(2);
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 重写以支持不同类型控件的界面更新
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                console.log(`[FilterControlParameterManager] updateCurrentValueFromMQTT 被调用: mqttId=${mqttId}, value=${value}, type=${typeof value}`);

                const param = this.parameters.find(p => p.mqttId === mqttId);
                if (!param) {
                    console.warn(`[FilterControlParameterManager] 未找到参数: mqttId=${mqttId}`);
                    return;
                }

                const paramConfig = this.config.parameters.find(p => p.mqttId === mqttId);
                if (!paramConfig) {
                    console.warn(`[FilterControlParameterManager] 未找到参数配置: mqttId=${mqttId}`);
                    return;
                }

                // 根据参数类型处理数值
                let processedValue = value;
                if (paramConfig.type === 'float') {
                    processedValue = parseFloat(value);
                    if (isNaN(processedValue)) {
                        console.warn(`[FilterControlParameterManager] 参数 ${param.name} 收到无效浮点数值: ${value}`);
                        return;
                    }
                } else if (paramConfig.type === 'integer') {
                    processedValue = parseInt(value, 10);
                    if (isNaN(processedValue)) {
                        console.warn(`[FilterControlParameterManager] 参数 ${param.name} 收到无效整数值: ${value}`);
                        return;
                    }
                } else {
                    // switch类型，直接使用数值
                    processedValue = parseInt(value, 10);
                    if (isNaN(processedValue)) {
                        processedValue = 0;
                    }
                }

                // 更新参数当前值
                param.currentValue = processedValue;

                // 首次数据同步：如果参数未初始化，将设定值设为当前值
                if (!param.isInitialized) {
                    param.settingValue = param.currentValue;
                    param.isInitialized = true;

                    // 根据控件类型更新界面
                    this.updateControlInterface(param, paramConfig);

                    // 记录修改
                    this.modifiedValues[param.id] = param.settingValue;

                    console.log(`[FilterControlParameterManager] 首次同步参数 ${param.name}: 当前值=${processedValue}, 设定值=${processedValue} (已初始化)`);
                }

                // 更新界面显示（当前值）
                const currentElement = document.getElementById(`current-${param.id}`);
                if (currentElement) {
                    const displayText = this.getCurrentValueDisplayText(param, paramConfig);
                    currentElement.textContent = displayText;
                    currentElement.style.color = '#00d4ff';

                    console.log(`[FilterControlParameterManager] 更新参数 ${param.name} 显示: 原始值=${value}, 处理值=${processedValue}, 格式化值=${displayText}`);
                } else {
                    console.warn(`[FilterControlParameterManager] 未找到当前值显示元素: current-${param.id}`);
                }

                // 更新高亮状态
                this.updateHighlightStatus(param.id);
            }

            /**
             * 根据控件类型更新界面
             * @param {Object} param - 参数对象
             * @param {Object} config - 参数配置
             */
            updateControlInterface(param, config) {
                switch (config.type) {
                    case 'switch':
                        this.updateSwitchInterface(param, config);
                        break;
                    case 'integer':
                        this.updateIntegerInterface(param, config);
                        break;
                    case 'float':
                        this.updateFloatInterface(param, config);
                        break;
                }
            }

            /**
             * 更新开关控件界面
             */
            updateSwitchInterface(param, config) {
                const toggleElement = document.getElementById(`toggle-${param.id}`);
                const statusTextElement = document.getElementById(`status-text-${param.id}`);

                if (toggleElement) {
                    if (param.settingValue) {
                        toggleElement.classList.add('active');
                    } else {
                        toggleElement.classList.remove('active');
                    }
                }

                if (statusTextElement) {
                    if (param.settingValue) {
                        statusTextElement.classList.add('active');
                    } else {
                        statusTextElement.classList.remove('active');
                    }

                    if (config.options) {
                        statusTextElement.textContent = config.options[param.settingValue] || param.settingValue;
                    } else {
                        statusTextElement.textContent = param.settingValue;
                    }
                }
            }

            /**
             * 更新整数输入控件界面
             */
            updateIntegerInterface(param, config) {
                const inputElement = document.getElementById(`input-${param.id}`);
                if (inputElement) {
                    inputElement.value = param.settingValue;
                }
            }

            /**
             * 更新浮点数输入控件界面
             */
            updateFloatInterface(param, config) {
                const inputElement = document.getElementById(`input-${param.id}`);
                if (inputElement) {
                    inputElement.value = param.settingValue.toFixed(2);
                }
            }
        }

        // 全局滤波控制管理器变量
        let filterControlManager = null;

        /**
         * 检查参数管理器状态
         * @returns {boolean} 参数管理器是否已正确初始化
         */
        function checkParameterManagerStatus() {
            console.log('检查参数管理器状态...');
            console.log('filterControlManager:', filterControlManager);
            console.log('window.parameterManager:', window.parameterManager);

            if (!filterControlManager) {
                console.error('filterControlManager 未初始化');
                return false;
            }

            if (!window.parameterManager) {
                console.error('window.parameterManager 未设置');
                return false;
            }

            console.log('参数管理器状态检查通过');
            return true;
        }

        /**
         * 处理参数设置发送（滤波控制专用）
         */
        async function handleSendParameterSettings() {
            console.log('开始发送滤波控制参数设置...');

            // 检查参数管理器状态
            if (!checkParameterManagerStatus()) {
                showStatusMessage('参数管理器未正确初始化，请刷新页面重试', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            if (!mqttParameterManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            const connectionStatus = mqttParameterManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = filterControlManager.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }

            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = filterControlManager.getMQTTParameterArray();

                console.log('准备发送的滤波控制参数:', mqttParams);

                // 发送参数设置
                const result = await mqttParameterManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `滤波控制参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('滤波控制参数设置发送成功:', result);

            } catch (error) {
                console.error('发送滤波控制参数设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 初始化滤波控制配置页面
         * @param {Object} config - 滤波控制配置对象
         */
        function initFilterControlConfigPage(config) {
            console.log('滤波控制配置页面初始化...');

            try {
                // 初始化滤波控制参数管理器
                filterControlManager = new FilterControlParameterManager(config);

                // 设置全局变量以兼容通用脚本
                window.parameterManager = filterControlManager;

                // 创建备用的全局发送函数
                window.sendParameterSettings = handleSendParameterSettings;

                console.log('滤波控制参数管理器初始化成功:', filterControlManager);
                console.log('全局参数管理器设置成功:', window.parameterManager);

                // 初始化 MQTT 连接
                initMQTTConnection();

                // 定期更新连接状态显示和按钮状态
                setInterval(() => {
                    if (mqttParameterManager) {
                        const status = mqttParameterManager.getConnectionStatus();
                        if (status.isConnected) {
                            updateMQTTStatus('connected', 'MQTT 已连接');
                        } else {
                            updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                        }
                        // 更新发送按钮状态
                        updateSendButtonStatus();
                    }
                }, 1000);

                console.log('滤波控制配置页面初始化完成');

            } catch (error) {
                console.error('滤波控制配置页面初始化失败:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        }

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 内容加载完成，开始初始化滤波控制配置页面...');

            // 检查必要的依赖
            if (typeof ParameterConfigManager === 'undefined') {
                console.error('ParameterConfigManager 类未定义，请检查通用脚本是否正确加载');
                showStatusMessage('页面初始化失败：缺少必要的依赖脚本', 'error');
                return;
            }

            if (typeof filterControlConfig === 'undefined') {
                console.error('filterControlConfig 配置对象未定义');
                showStatusMessage('页面初始化失败：缺少配置对象', 'error');
                return;
            }

            console.log('依赖检查通过，开始初始化滤波控制参数管理器...');
            console.log('滤波控制配置:', filterControlConfig);

            // 使用自定义的滤波控制参数配置管理器初始化页面
            try {
                initFilterControlConfigPage(filterControlConfig);

                // 延迟检查初始化状态
                setTimeout(() => {
                    if (checkParameterManagerStatus()) {
                        console.log('滤波控制参数管理器初始化验证成功');
                        showStatusMessage('页面初始化完成', 'success');
                    } else {
                        console.error('滤波控制参数管理器初始化验证失败');
                        showStatusMessage('参数管理器初始化异常，请刷新页面', 'error');
                    }
                }, 1000);

            } catch (error) {
                console.error('页面初始化过程中发生错误:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
